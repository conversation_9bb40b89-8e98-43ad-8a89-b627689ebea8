/**
 * Rating Component
 * Displays company ratings and feedback for candidates
 */
import React from 'react';
import { Card, Rate, Typography, Avatar, Tag, Empty, Divider, Progress } from 'antd';
import { Star, Building2, Calendar, TrendingUp, User } from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Title, Text, Paragraph } = Typography;

const Rating = ({ ratings = [], showSummary = true, size = 'default' }) => {
  // Calculate rating statistics
  const calculateRatingStats = () => {
    if (!ratings || ratings.length === 0) {
      return {
        averageRating: 0,
        totalRatings: 0,
        distribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
        categories: {},
      };
    }

    const totalRatings = ratings.length;
    const totalScore = ratings.reduce((sum, rating) => sum + rating.rating, 0);
    const averageRating = totalScore / totalRatings;

    // Rating distribution
    const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    ratings.forEach((rating) => {
      distribution[rating.rating] = (distribution[rating.rating] || 0) + 1;
    });

    // Category averages
    const categories = {};
    const categoryGroups = {};

    ratings.forEach((rating) => {
      if (rating.categories) {
        Object.entries(rating.categories).forEach(([category, score]) => {
          if (!categoryGroups[category]) {
            categoryGroups[category] = [];
          }
          categoryGroups[category].push(score);
        });
      }
    });

    Object.entries(categoryGroups).forEach(([category, scores]) => {
      categories[category] = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    });

    return {
      averageRating,
      totalRatings,
      distribution,
      categories,
    };
  };

  const stats = calculateRatingStats();

  const getRatingColor = (rating) => {
    if (rating >= 4.5) return '#52c41a';
    if (rating >= 3.5) return '#faad14';
    if (rating >= 2.5) return '#fa8c16';
    return '#ff4d4f';
  };

  const formatRating = (rating) => {
    return Number(rating).toFixed(1);
  };

  // Compact view for headers
  if (size === 'compact') {
    return (
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <Star
            size={16}
            className="text-yellow-500"
            fill="currentColor"
          />
          <Text
            strong
            className="text-lg"
          >
            {formatRating(stats.averageRating)}
          </Text>
        </div>
        <div className="text-gray-500 text-sm">
          {stats.totalRatings} {stats.totalRatings === 1 ? 'review' : 'reviews'}
        </div>
      </div>
    );
  }

  if (!ratings || ratings.length === 0) {
    return (
      <Card
        title={
          <span className="flex items-center">
            <StarOutlined className="mr-2 text-yellow-500" />
            Company Ratings
          </span>
        }
        className="shadow-sm rounded-lg"
      >
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <div className="text-center">
              <Text type="secondary">No ratings yet</Text>
              <div className="text-xs text-gray-400 mt-1">
                Ratings will appear here after interviews
              </div>
            </div>
          }
        />
      </Card>
    );
  }

  return (
    <Card
      title={
        <span className="flex items-center">
          <StarOutlined className="mr-2 text-yellow-500" />
          Company Ratings & Feedback
        </span>
      }
      className="shadow-sm rounded-lg"
    >
      {showSummary && (
        <>
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="text-center">
                  <div
                    className="text-3xl font-bold"
                    style={{ color: getRatingColor(stats.averageRating) }}
                  >
                    {formatRating(stats.averageRating)}
                  </div>
                  <Rate
                    disabled
                    value={stats.averageRating}
                    allowHalf
                    className="text-sm"
                  />
                </div>
                <div>
                  <Text className="text-gray-600">
                    Based on {stats.totalRatings} {stats.totalRatings === 1 ? 'review' : 'reviews'}
                  </Text>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <TrendingUp
                  size={16}
                  className="text-green-500"
                />
                <Tag
                  color="green"
                  className="px-3 py-1"
                >
                  {stats.averageRating >= 4
                    ? 'Excellent'
                    : stats.averageRating >= 3
                      ? 'Good'
                      : 'Average'}{' '}
                  Performance
                </Tag>
              </div>
            </div>

            {/* Rating Distribution */}
            <div className="grid grid-cols-5 gap-2 mb-4">
              {[5, 4, 3, 2, 1].map((star) => (
                <div
                  key={star}
                  className="flex items-center space-x-2"
                >
                  <span className="text-sm w-3">{star}</span>
                  <Star
                    size={12}
                    className="text-yellow-500"
                    fill="currentColor"
                  />
                  <Progress
                    percent={(stats.distribution[star] / stats.totalRatings) * 100}
                    showInfo={false}
                    size="small"
                    strokeColor="#faad14"
                    className="flex-1"
                  />
                  <span className="text-xs text-gray-500 w-6">{stats.distribution[star]}</span>
                </div>
              ))}
            </div>

            {/* Category Ratings */}
            {Object.keys(stats.categories).length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(stats.categories).map(([category, rating]) => (
                  <div
                    key={category}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <span className="font-medium text-gray-700 capitalize">{category}</span>
                    <div className="flex items-center space-x-2">
                      <Rate
                        disabled
                        value={rating}
                        allowHalf
                        className="text-xs"
                      />
                      <span
                        className="font-bold"
                        style={{ color: getRatingColor(rating) }}
                      >
                        {formatRating(rating)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <Divider />
        </>
      )}

      {/* Individual Ratings */}
      <div className="space-y-4">
        <Title
          level={5}
          className="mb-3"
        >
          Recent Reviews
        </Title>
        {ratings.slice(0, 5).map((rating, index) => (
          <div
            key={index}
            className="border border-gray-100 rounded-lg p-4 hover:shadow-sm transition-shadow"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <Avatar
                  src={rating.company?.logo}
                  icon={<Building2 size={16} />}
                  className="bg-blue-100"
                />
                <div>
                  <Text
                    strong
                    className="text-base"
                  >
                    {rating.company?.name || 'Anonymous Company'}
                  </Text>
                  <div className="flex items-center space-x-2 mt-1">
                    <Rate
                      disabled
                      value={rating.rating}
                      className="text-sm"
                    />
                    <span
                      className="font-semibold"
                      style={{ color: getRatingColor(rating.rating) }}
                    >
                      {rating.rating}/5
                    </span>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center text-gray-500 text-sm">
                  <Calendar
                    size={12}
                    className="mr-1"
                  />
                  {dayjs(rating.created_at).fromNow()}
                </div>
                {rating.position && (
                  <Text
                    type="secondary"
                    className="text-xs"
                  >
                    {rating.position}
                  </Text>
                )}
              </div>
            </div>

            {rating.feedback && (
              <Paragraph className="text-gray-600 mb-3 text-sm leading-relaxed">
                "{rating.feedback}"
              </Paragraph>
            )}

            {rating.categories && Object.keys(rating.categories).length > 0 && (
              <div className="flex flex-wrap gap-2">
                {Object.entries(rating.categories).map(([category, score]) => (
                  <Tag
                    key={category}
                    color="blue"
                    className="text-xs"
                  >
                    {category}: {score}/5
                  </Tag>
                ))}
              </div>
            )}
          </div>
        ))}

        {ratings.length > 5 && (
          <div className="text-center pt-4">
            <Text type="secondary">Showing 5 of {ratings.length} reviews</Text>
          </div>
        )}
      </div>
    </Card>
  );
};

export default Rating;
