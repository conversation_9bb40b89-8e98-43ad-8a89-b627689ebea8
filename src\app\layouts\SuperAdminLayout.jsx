import React, { useState, useEffect } from 'react';
import { Layout, Switch, Spin, Badge } from 'antd';
import { Outlet, Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Shield,
  Users,
  Activity,
  Settings,
  BarChart3,
  MessageSquare,
  User,
  Calendar,
  FileText,
  HelpCircle,
  LogOut,
  Lightbulb,
  UserPlus,
  Headphones,
  LayoutDashboard,
  ScrollText,
} from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';
import PageTitle from '@/components/PageTitle';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useColorModeStore } from '@/store/colorMode.store';
import useAuth from '@/hooks/useAuth';
import { logo_lite, logo_dark } from '@/assets';
import SearchModal from '@/components/shared/SearchModal';
import AppSidebar from '@/components/layouts/AppSidebar';
import AppHeader from '@/components/layouts/AppHeader';
import MobileDrawer from '@/components/layouts/MobileDrawer';
import showToast from '@/utils/toast';

const { Content, Footer } = Layout;

// Icon style for consistent sizing
const iconStyle = { fontSize: '18px' };

const SuperAdminLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery('(max-width: 767px)');
  const isTablet = useMediaQuery('(max-width: 991px)');
  const { colorMode, toggleColorMode } = useColorModeStore();
  const isDark = colorMode === 'dark';

  // Auth state from useAuth hook
  const { profile, logout, loading, isSuperAdmin, hasPermission, permissions, teamMemberships } =
    useAuth();

  // Local UI state
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerOpen, setMobileDrawerOpen] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [systemAlerts, setSystemAlerts] = useState([]);

  // Check if user has super admin access
  useEffect(() => {
    if (!loading && !isSuperAdmin()) {
      navigate('/access-denied', { replace: true });
    }
  }, [loading, isSuperAdmin, navigate]);

  // Initialize mock data for demo
  useEffect(() => {
    setNotifications([
      { id: 1, title: 'New user registration pending approval', read: false, type: 'approval' },
      { id: 2, title: 'System backup completed', read: false, type: 'system' },
      {
        id: 3,
        title: 'Security alert: Multiple failed login attempts',
        read: false,
        type: 'security',
      },
      { id: 4, title: 'Monthly analytics report ready', read: true, type: 'report' },
      { id: 5, title: 'Team member added to Approvers', read: true, type: 'team' },
    ]);

    setSystemAlerts([
      {
        id: 1,
        title: 'High Priority: Interview Assignment Failed',
        severity: 'high',
        time: '5 min ago',
        description: 'Auto-assignment failed for 3 interviews',
      },
      {
        id: 2,
        title: 'System Maintenance Scheduled',
        severity: 'info',
        time: '1 hour ago',
        description: 'Scheduled maintenance on Sunday 2:00 AM',
      },
    ]);
  }, []);

  // Collapse sidebar on mobile by default
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    }
  }, [isMobile]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileDrawer = () => {
    setMobileDrawerOpen(!mobileDrawerOpen);
  };

  const handleThemeToggle = () => {
    toggleColorMode();
    showToast.success(`Switched to ${isDark ? 'light' : 'dark'} mode`);
  };

  const handleLogout = async () => {
    try {
      const { success } = await logout();
      if (success) {
        showToast.success('Logged out successfully');
      }
    } catch (error) {
      showToast.error('Failed to logout');
    }
  };

  const notificationItems = notifications.map((notification) => ({
    key: notification.id,
    label: (
      <div
        className={`notification-item ${!notification.read ? 'font-bold' : ''} p-2`}
        style={{ fontSize: '16px' }}
      >
        <div className="flex items-center justify-between">
          <span>{notification.title}</span>
          <Badge
            color={
              notification.type === 'security'
                ? 'red'
                : notification.type === 'approval'
                  ? 'orange'
                  : notification.type === 'system'
                    ? 'blue'
                    : 'green'
            }
          />
        </div>
      </div>
    ),
  }));

  const alertItems = systemAlerts.map((alert) => ({
    key: alert.id,
    label: (
      <div className="p-2">
        <div className="flex items-center justify-between">
          <div
            style={{ fontSize: '16px' }}
            className="font-semibold"
          >
            {alert.title}
          </div>
          <Badge
            color={
              alert.severity === 'high' ? 'red' : alert.severity === 'warning' ? 'orange' : 'blue'
            }
          />
        </div>
        <div
          style={{ fontSize: '14px' }}
          className="text-gray-500"
        >
          {alert.time}
        </div>
        <div style={{ fontSize: '14px' }}>{alert.description}</div>
      </div>
    ),
  }));

  const userMenuItems = [
    {
      key: 'profile',
      icon: <User style={iconStyle} />,
      label: (
        <Link
          to="/admin/profile"
          style={{ fontSize: '16px' }}
        >
          Profile
        </Link>
      ),
    },
    {
      key: 'settings',
      icon: <Settings style={iconStyle} />,
      label: (
        <Link
          to="/admin/settings"
          style={{ fontSize: '16px' }}
        >
          System Settings
        </Link>
      ),
    },
    {
      key: 'theme',
      icon: isDark ? <Lightbulb style={iconStyle} /> : <Lightbulb style={iconStyle} />,
      label: (
        <div className="flex items-center justify-between">
          <span style={{ fontSize: '16px' }}>Dark Mode</span>
          <Switch
            checked={isDark}
            onChange={handleThemeToggle}
            size="default"
            className="ml-2"
          />
        </div>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'help',
      icon: <HelpCircle style={iconStyle} />,
      label: (
        <Link
          to="/help"
          style={{ fontSize: '16px' }}
        >
          Help & Support
        </Link>
      ),
    },
    {
      key: 'logout',
      icon: <LogOut style={iconStyle} />,
      label: <span style={{ fontSize: '16px' }}>{loading ? <Spin size="small" /> : 'Logout'}</span>,
      onClick: handleLogout,
    },
  ];

  // Dynamic sidebar items based on permissions
  const sidebarItems = [
    {
      key: 'dashboard',
      icon: <LayoutDashboard style={iconStyle} />,
      label: (
        <Link
          to="/admin/dashboard"
          style={{ fontSize: '16px' }}
        >
          Dashboard
        </Link>
      ),
    },
    // User Management - visible if user has users.view permission
    ...(hasPermission('users.view')
      ? [
          {
            key: 'users',
            icon: <Users style={iconStyle} />,
            label: (
              <Link
                to="/admin/users"
                style={{ fontSize: '16px' }}
              >
                User Management
              </Link>
            ),
          },
        ]
      : []),
    // Team Management - visible if user has teams.view permission
    ...(hasPermission('teams.view')
      ? [
          {
            key: 'teams',
            icon: <UserPlus style={iconStyle} />,
            label: (
              <Link
                to="/admin/teams"
                style={{ fontSize: '16px' }}
              >
                Team Management
              </Link>
            ),
          },
        ]
      : []),
    // Interview Management - visible if user has interviews.view permission
    ...(hasPermission('interviews.view')
      ? [
          {
            key: 'interviews',
            icon: <Calendar style={iconStyle} />,
            label: (
              <Link
                to="/admin/interviews"
                style={{ fontSize: '16px' }}
              >
                Interview Management
              </Link>
            ),
          },
        ]
      : []),
    // Analytics - visible if user has analytics.view permission
    ...(hasPermission('analytics.view')
      ? [
          {
            key: 'analytics',
            icon: <BarChart3 style={iconStyle} />,
            label: (
              <Link
                to="/admin/analytics"
                style={{ fontSize: '16px' }}
              >
                Analytics & Reports
              </Link>
            ),
          },
        ]
      : []),
    // Support Management - visible if user has support.tickets permission
    ...(hasPermission('support.tickets')
      ? [
          {
            key: 'support',
            icon: <Headphones style={iconStyle} />,
            label: (
              <Link
                to="/admin/support"
                style={{ fontSize: '16px' }}
              >
                Support Management
              </Link>
            ),
          },
        ]
      : []),
    // Audit Logs - visible if user has system.audit permission
    ...(hasPermission('system.audit')
      ? [
          {
            key: 'audit',
            icon: <AuditOutlined style={iconStyle} />,
            label: (
              <Link
                to="/admin/audit"
                style={{ fontSize: '16px' }}
              >
                Audit Logs
              </Link>
            ),
          },
        ]
      : []),
    // System Settings - visible if user has system.settings permission
    ...(hasPermission('system.settings')
      ? [
          {
            key: 'settings',
            icon: <Settings style={iconStyle} />,
            label: (
              <Link
                to="/admin/settings"
                style={{ fontSize: '16px' }}
              >
                System Settings
              </Link>
            ),
          },
        ]
      : []),
  ];

  return (
    <Layout className="min-h-screen">
      <PageTitle
        title="Super Admin Panel"
        description="Manage platform operations and administration"
      />
      <AppSidebar
        collapsed={collapsed}
        sidebarItems={sidebarItems}
        selectedKey={location.pathname.split('/')[2] || 'dashboard'}
        isDark={isDark}
        profile={profile}
        handleLogout={handleLogout}
        isLoggingOut={loading}
      />

      {isMobile && (
        <MobileDrawer
          open={mobileDrawerOpen}
          onClose={toggleMobileDrawer}
          sidebarItems={sidebarItems}
          selectedKey={location.pathname.split('/')[2] || 'dashboard'}
          isDark={isDark}
          profile={profile}
          handleThemeToggle={handleThemeToggle}
          handleLogout={handleLogout}
          isLoggingOut={loading}
        />
      )}

      <Layout
        style={{
          marginLeft: isMobile ? 0 : collapsed ? 80 : 250,
          transition: 'all 0.2s',
          minHeight: '100vh',
        }}
      >
        <AppHeader
          collapsed={collapsed}
          toggleCollapsed={toggleCollapsed}
          toggleMobileDrawer={toggleMobileDrawer}
          isMobile={isMobile}
          isTablet={isTablet}
          isDark={isDark}
          logo_dark={logo_dark}
          logo_lite={logo_lite}
          notificationItems={notificationItems}
          userMenuItems={userMenuItems}
          setSearchModalVisible={setSearchModalVisible}
          notifications={notifications}
          profile={profile}
          interviewItems={alertItems}
          upcomingInterviews={systemAlerts}
          handleThemeToggle={handleThemeToggle}
          showInterviews={true}
          adminMode={true}
        />

        <Content
          className={`p-2 sm:p-4 transition-all`}
          style={{ minHeight: 280 }}
        >
          <div className="bg-card rounded-lg p-3 sm:p-4 shadow-sm">
            <Outlet />
          </div>
        </Content>

        <Footer
          style={{
            textAlign: 'center',
            padding: isMobile ? '8px 16px' : '12px 50px',
            fontSize: isMobile ? '14px' : '16px',
          }}
        >
          InterviewPro ©{new Date().getFullYear()} - Super Admin Panel
        </Footer>
      </Layout>

      <SearchModal
        visible={searchModalVisible}
        onClose={() => setSearchModalVisible(!searchModalVisible)}
      />
    </Layout>
  );
};

export default SuperAdminLayout;
