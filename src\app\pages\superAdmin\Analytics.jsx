import React from 'react';
import { <PERSON>, But<PERSON>, Space } from 'antd';
import { BarChart3, Download } from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';

const Analytics = () => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics & Reports</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Platform insights and performance metrics
          </p>
        </div>
        <Space>
          <Button icon={<LucideIcon icon={Download} />}>Export Report</Button>
        </Space>
      </div>

      <Card>
        <div className="text-center py-12">
          <LucideIcon
            icon={BarChart3}
            className="text-6xl text-gray-400 mb-4"
            size={64}
          />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Analytics Dashboard Coming Soon
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            This feature will provide comprehensive analytics and reporting capabilities.
          </p>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;
