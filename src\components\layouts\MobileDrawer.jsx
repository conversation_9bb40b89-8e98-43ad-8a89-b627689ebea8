import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Divider, Switch } from 'antd';
import { User, LogOut, Lightbulb } from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';
import useAuth from '@/hooks/useAuth';
import showToast from '@/utils/toast';

const MobileDrawer = ({
  open,
  onClose,
  sidebarItems,
  selectedKey,
  isDark,
  profile,
  handleThemeToggle,
  handleLogout,
  isLoggingOut,
}) => {
  return (
    <Drawer
      title={
        <div className="flex items-center">
          <Avatar
            size="small"
            icon={<UserOutlined />}
            className="mr-2"
            src={profile?.profile_photo_url}
          />
          <div className="overflow-hidden">
            <div className="font-semibold truncate">
              {profile?.full_name || profile?.email?.split('@')[0] || 'User'}
            </div>
            <div className="text-xs opacity-70 truncate">
              {profile?.role || (profile?.email ? profile.email : '<EMAIL>')}
            </div>
          </div>
        </div>
      }
      placement="left"
      onClose={onClose}
      open={open}
      width={280}
      styles={{
        body: { padding: 0 },
        header: { padding: '12px 16px' },
      }}
    >
      <Menu
        mode="inline"
        selectedKeys={[selectedKey]}
        items={sidebarItems}
        style={{ border: 'none' }}
        theme={isDark ? 'dark' : 'light'}
        onClick={onClose}
      />

      <Divider style={{ margin: '8px 0' }} />

      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <span className="flex items-center">
            <LucideIcon
              icon={Lightbulb}
              className="mr-2"
            />
            Dark Mode
          </span>
          <Switch
            checked={isDark}
            onChange={handleThemeToggle}
            size="small"
          />
        </div>

        <Button
          type="primary"
          danger
          icon={<LucideIcon icon={LogOut} />}
          onClick={handleLogout}
          block
          loading={isLoggingOut}
        >
          {isLoggingOut ? 'Logging out...' : 'Logout'}
        </Button>
      </div>
    </Drawer>
  );
};

export default MobileDrawer;
