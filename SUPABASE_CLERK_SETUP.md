# Supabase + Clerk Integration Setup

## Overview
This document outlines the complete setup of Supabase Row Level Security (RLS) policies for Clerk authentication integration in the Interview Management SaaS Platform.

## Database Structure

### Core Tables
- `profiles` - Main user profiles with basic info
- `company_profiles` - Company-specific profile data
- `interviewer_profiles` - Interviewer-specific profile data
- `interviews` - Interview records and management
- `jobs` - Job postings by companies
- `calendar_events` - User calendar events
- `interview_invitations` - Interview invitation system
- `interviewer_earnings` - Earnings tracking for interviewers

### User Roles
- `company` - Companies that post jobs and hire interviewers
- `interviewer` - Professionals who conduct interviews
- `super_admin` - Platform administrators with full access

## Clerk Integration Functions

### Authentication Functions
- `public.clerk_user_id()` - Extracts user ID from Clerk JWT
- `public.clerk_user_role()` - Extracts user role from Clerk JWT metadata
- `public.is_super_admin()` - Checks if current user is super admin
- `public.is_company()` - Checks if current user is a company
- `public.is_interviewer()` - Checks if current user is an interviewer
- `public.current_user_profile()` - Returns current user's profile

### Webhook Handler
- `handle_clerk_user_webhook(webhook_data jsonb)` - Processes Clerk user events
  - Handles user.created, user.updated, user.deleted events
  - Automatically creates role-specific profiles
  - Syncs user data between Clerk and Supabase

### Utility Functions
- `public.complete_user_profile()` - Completes user profile setup
- `public.update_user_status()` - Super admin function for user management

## RLS Policies Summary

### Profiles Table
- Users can view/update their own profile
- Super admins can view/update all profiles
- Companies and interviewers can view basic profile info of each other

### Company Profiles
- Companies can manage their own profile
- Interviewers can view company profiles
- Super admins have full access

### Interviewer Profiles
- Interviewers can manage their own profile
- Companies can view interviewer profiles
- Super admins have full access

### Interviews
- Companies can manage interviews they create
- Interviewers can view/accept available requests
- Interviewers can update their assigned interviews
- Super admins have full access

### Jobs
- Companies can manage their own jobs
- Interviewers can view active jobs
- Super admins have full access

### Calendar Events
- Users can manage their own calendar events
- Super admins have full access

### Interview Invitations
- Users can manage invitations they send
- Recipients can view/update invitations sent to them
- Super admins have full access

### Interviewer Earnings
- Interviewers can view their own earnings
- Super admins can manage all earnings
- System can insert earnings (for automated processes)

## Database Views

### Complete Profile Views
- `company_profiles_complete` - Joined view of profiles + company_profiles
- `interviewer_profiles_complete` - Joined view of profiles + interviewer_profiles

## Performance Optimizations

### Indexes Created
- `idx_profiles_role` - For role-based queries
- `idx_profiles_email` - For email lookups
- `idx_interviews_company_id` - For company interview queries
- `idx_interviews_interviewer_id` - For interviewer queries
- `idx_interviews_status` - For status-based filtering
- `idx_jobs_company_id` - For company job queries
- `idx_jobs_status` - For job status filtering
- `idx_calendar_events_user_id` - For user calendar queries
- `idx_interview_invitations_invited_by` - For invitation sender queries
- `idx_interview_invitations_recipient_email` - For recipient queries
- `idx_interviewer_earnings_interviewer_id` - For earnings queries

## Security Features

### Row Level Security
- All tables have RLS enabled
- Policies enforce user isolation based on Clerk JWT
- Role-based access control implemented
- Super admin override capabilities

### JWT Integration
- Custom functions extract user data from Clerk JWT
- Supports both public_metadata and unsafe_metadata for roles
- Graceful fallback handling for missing JWT data

## Next Steps

### Frontend Integration
1. Configure Clerk in your React app
2. Set up Supabase client with JWT from Clerk
3. Use the created views and functions for data access

### Webhook Setup
1. Configure Clerk webhook endpoint
2. Point to Supabase Edge Function that calls `handle_clerk_user_webhook`
3. Test user creation, update, and deletion flows

### Testing
1. Test all RLS policies with different user roles
2. Verify JWT extraction functions work correctly
3. Test webhook handler with sample Clerk events

## Configuration Notes

- Default role is 'company' if not specified in Clerk metadata
- Phone number is optional and defaults to empty string
- Email verification status is synced from Clerk
- User deletion cascades to related profile tables
