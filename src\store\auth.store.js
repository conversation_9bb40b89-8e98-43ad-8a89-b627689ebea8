import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * Authentication & Profile Store (Clean Data Store)
 *
 * Responsibilities:
 * - Authentication state management (user, role, profile data)
 * - UI state management (loading, error states)
 * - Profile completion calculations
 * - Cache management for profile data
 * - State setters and getters only
 *
 * Note: All authentication is handled by Clerk, profile data via Supabase
 * This store focuses purely on state management using Zustand
 *
 * Follows standardized store patterns:
 * - Consistent naming conventions
 * - Standardized loading/error states
 * - Unified cache management
 * - Pure state management only
 */

const initialAuthState = {
  // Core authentication state
  user: null,
  role: null,
  rolePath: null,
  isAuthenticated: false,

  // Profile state (centralized)
  profile: null,
  profileCompletion: 0,

  // Super Admin specific state
  adminLevel: null, // 'super_admin' or 'admin'
  permissions: [],
  teamMemberships: [],

  // UI state
  loading: false,
  error: null,

  // Internal state
  _initialized: false,

  // Cache management (standardized)
  _cache: {
    lastProfileFetch: null,
    profileExpiry: 5 * 60 * 1000, // 5 minutes
    lastPermissionsFetch: null,
    permissionsExpiry: 10 * 60 * 1000, // 10 minutes
  },
};

// Helper functions for profile management
const calculateProfileCompletion = (profile, role) => {
  if (!profile || !role) return 0;

  const roleFieldMap = {
    company: ['email', 'phone_number', 'company_name', 'company_size', 'industry', 'website'],
    interviewer: ['email', 'phone_number', 'full_name', 'department', 'years_experience'],
  };

  const requiredFields = roleFieldMap[role] || [];
  const filledFields = requiredFields.filter(
    (field) => profile[field] && profile[field].toString().trim() !== ''
  ).length;

  return Math.round((filledFields / requiredFields.length) * 100);
};

const mapRoleToPath = (role) => {
  const rolePathMap = {
    company: 'org',
    interviewer: 'sourcer',
    super_admin: 'admin',
  };
  return rolePathMap[role] || role;
};

const useAuthStore = create(
  devtools(
    (set, get) => ({
      ...initialAuthState,

      // === CORE AUTH SETTERS ===
      setUser: (user) => {
        set(
          {
            user,
            isAuthenticated: !!user,
          },
          false,
          'auth:setUser'
        );
      },

      setRole: (role) => {
        const rolePath = mapRoleToPath(role);
        const { profile } = get();
        const profileCompletion = calculateProfileCompletion(profile, role);

        set(
          {
            role,
            rolePath,
            profileCompletion,
          },
          false,
          'auth:setRole'
        );
      },

      setProfile: (profile) => {
        const { role } = get();
        const profileCompletion = calculateProfileCompletion(profile, role);

        set(
          {
            profile,
            profileCompletion,
          },
          false,
          'auth:setProfile'
        );
      },

      updateProfile: (updates) => {
        const { profile, role } = get();
        const updatedProfile = { ...(profile || {}), ...updates };
        const profileCompletion = calculateProfileCompletion(updatedProfile, role);

        set(
          {
            profile: updatedProfile,
            profileCompletion,
          },
          false,
          'auth:updateProfile'
        );
      },

      // === SUPER ADMIN SPECIFIC SETTERS ===
      setAdminLevel: (adminLevel) => {
        set({ adminLevel }, false, 'auth:setAdminLevel');
      },

      setPermissions: (permissions) => {
        set({ permissions }, false, 'auth:setPermissions');
      },

      setTeamMemberships: (teamMemberships) => {
        set({ teamMemberships }, false, 'auth:setTeamMemberships');
      },

      // Check if user has specific permission
      hasPermission: (permissionName) => {
        const { permissions, adminLevel } = get();

        // Super admins have all permissions
        if (adminLevel === 'super_admin') return true;

        // Check if permission exists in user's permissions array
        return (
          Array.isArray(permissions) &&
          permissions.some((p) => p && p.permission_name === permissionName)
        );
      },

      // Check if user is super admin
      isSuperAdmin: () => {
        const { role, adminLevel } = get();
        return role === 'super_admin' && adminLevel === 'super_admin';
      },

      // Check if user is any type of admin
      isAdmin: () => {
        const { role } = get();
        return role === 'super_admin';
      },

      // === STANDARDIZED UI STATE HELPERS ===
      setLoading: (loading) => set({ loading }, false, 'auth:setLoading'),
      setError: (error) => set({ error }, false, 'auth:setError'),
      clearError: () => set({ error: null }, false, 'auth:clearError'),

      // === STANDARDIZED CACHE HELPERS ===
      updateProfileCache: () => {
        const { _cache } = get();
        set(
          {
            _cache: {
              ..._cache,
              lastProfileFetch: Date.now(),
            },
          },
          false,
          'auth:updateProfileCache'
        );
      },

      isProfileCacheValid: () => {
        const { _cache } = get();
        return (
          _cache.lastProfileFetch && Date.now() - _cache.lastProfileFetch < _cache.profileExpiry
        );
      },

      clearProfileCache: () => {
        const { _cache } = get();
        set(
          {
            _cache: {
              ..._cache,
              lastProfileFetch: null,
            },
          },
          false,
          'auth:clearProfileCache'
        );
      },

      // === PERMISSIONS CACHE HELPERS ===
      updatePermissionsCache: () => {
        const { _cache } = get();
        set(
          {
            _cache: {
              ..._cache,
              lastPermissionsFetch: Date.now(),
            },
          },
          false,
          'auth:updatePermissionsCache'
        );
      },

      isPermissionsCacheValid: () => {
        const { _cache } = get();
        return (
          _cache.lastPermissionsFetch &&
          Date.now() - _cache.lastPermissionsFetch < _cache.permissionsExpiry
        );
      },

      clearPermissionsCache: () => {
        const { _cache } = get();
        set(
          {
            _cache: {
              ..._cache,
              lastPermissionsFetch: null,
            },
          },
          false,
          'auth:clearPermissionsCache'
        );
      },

      // === INITIALIZATION STATE MANAGEMENT ===
      setInitialized: (initialized) => {
        set({ _initialized: initialized }, false, 'auth:setInitialized');
      },

      isInitialized: () => {
        return get()._initialized;
      },

      // === CLERK INTEGRATION ===
      // Clerk handles auth listeners automatically

      // === CLEANUP METHODS ===
      resetAuth: () => {
        // Clear only our app-specific auth storage
        if (typeof window !== 'undefined') {
          const keysToRemove = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (key.includes('auth-storage') || key.includes('user-storage'))) {
              keysToRemove.push(key);
            }
          }
          keysToRemove.forEach((key) => localStorage.removeItem(key));
        }

        // Reset to initial state (includes cache reset)
        set(initialAuthState, false, 'auth:resetAuth');
      },

      // === STANDARDIZED RESET METHOD ===
      resetStore: () => {
        get().resetAuth();
      },
    }),
    { name: 'auth-store' }
  )
);

export default useAuthStore;
