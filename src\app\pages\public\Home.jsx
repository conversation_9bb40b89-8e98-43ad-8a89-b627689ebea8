import {
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Row,
  Col,
  Card,
  Space,
  Divider,
  Avatar,
  Statistic,
  Tag,
  Carousel,
} from 'antd';
import {
  Rocket,
  Home as HomeIcon,
  User,
  Users,
  Calendar,
  Video,
  Building,
  ArrowRight,
  CheckCircle,
  Star,
  DollarSign,
  MapPin,
  Laptop,
  Shield,
  Zap,
  Trophy,
  Play,
  FileText,
  Smartphone,
  Globe,
  Handshake,
  TrendingUp,
  UserCheck,
} from 'lucide-react';
import LucideIcon from '@/components/shared/LucideIcon';

const { Title, Paragraph, Text } = Typography;

const Home = () => {
  const features = [
    {
      icon: <LucideIcon icon={Zap} />,
      title: 'AI Matching',
      description:
        'Our proprietary algorithm matches candidates with the perfect real estate positions based on skills and experience.',
      color: 'from-blue-500 to-cyan-400',
    },
    {
      icon: <LucideIcon icon={Calendar} />,
      title: 'Smart Scheduling',
      description: 'Automated scheduling system finds the perfect interview time for all parties.',
      color: 'from-purple-500 to-pink-400',
    },
    {
      icon: <LucideIcon icon={Video} />,
      title: 'Video Interviews',
      description: 'High-quality video platform with recording, notes, and evaluation tools.',
      color: 'from-amber-500 to-orange-400',
    },
    {
      icon: <LucideIcon icon={Shield} />,
      title: 'Verified Experts',
      description: 'All interviewers are verified real estate professionals with proven expertise.',
      color: 'from-emerald-500 to-teal-400',
    },
  ];

  const userTypes = [
    {
      icon: (
        <LucideIcon
          icon={UserCheck}
          size={24}
        />
      ),
      title: 'For Candidates',
      description:
        'Find your dream job in real estate with personalized matching and seamless interviews.',
      benefits: [
        'Access to exclusive real estate jobs',
        'Matched with relevant positions',
        'Prepare with AI tools',
      ],
      color: 'bg-gradient-to-br from-blue-500/90 to-blue-600/90',
      textColor: 'text-white',
      buttonText: 'Find Jobs',
    },
    {
      icon: (
        <LucideIcon
          icon={Building}
          size={24}
        />
      ),
      title: 'For Companies',
      description:
        'Hire top real estate talent faster with AI-powered matching and structured interviews.',
      benefits: [
        'Reduce time-to-hire by 50%',
        'Access pre-screened candidates',
        'Data-driven hiring decisions',
      ],
      color: 'bg-gradient-to-br from-emerald-500/90 to-emerald-600/90',
      textColor: 'text-white',
      buttonText: 'Post Jobs',
    },
    {
      icon: (
        <LucideIcon
          icon={Handshake}
          size={24}
        />
      ),
      title: 'For Interviewers',
      description:
        'Leverage your real estate expertise to conduct interviews and earn additional income.',
      benefits: [
        'Flexible scheduling',
        'Competitive compensation',
        'Grow your professional network',
      ],
      color: 'bg-gradient-to-br from-violet-500/90 to-violet-600/90',
      textColor: 'text-white',
      buttonText: 'Join as Interviewer',
    },
  ];

  const realEstateRoles = [
    { title: 'Real Estate Agent', icon: <LucideIcon icon={HomeIcon} />, count: 245 },
    { title: 'Property Manager', icon: <LucideIcon icon={Building} />, count: 187 },
    { title: 'Leasing Consultant', icon: <LucideIcon icon={Users} />, count: 156 },
    { title: 'Real Estate Broker', icon: <LucideIcon icon={DollarSign} />, count: 132 },
    { title: 'Sales Manager', icon: <LucideIcon icon={Trophy} />, count: 98 },
    {
      title: 'Business Development',
      icon: (
        <LucideIcon
          icon={TrendingUp}
          size={16}
        />
      ),
      count: 76,
    },
    { title: 'Property Consultant', icon: <LucideIcon icon={Laptop} />, count: 65 },
    { title: 'Facilities Manager', icon: <LucideIcon icon={MapPin} />, count: 54 },
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'HR Director at Prestige Properties',
      image: 'https://randomuser.me/api/portraits/women/44.jpg',
      text: "Flyt has revolutionized our hiring process. We've reduced our time-to-hire by 60% and found exceptional talent for our real estate team.",
    },
    {
      name: 'Michael Chen',
      role: 'Real Estate Agent',
      image: 'https://randomuser.me/api/portraits/men/32.jpg',
      text: 'Thanks to Flyt, I found my dream position at a top brokerage. The interview process was smooth and I felt well-prepared every step of the way.',
    },
    {
      name: 'Jessica Williams',
      role: 'Independent Interviewer',
      image: 'https://randomuser.me/api/portraits/women/63.jpg',
      text: 'As a real estate professional with 15+ years of experience, Flyt allows me to leverage my expertise while earning additional income on my own schedule.',
    },
  ];

  const appFeatures = [
    {
      icon: <LucideIcon icon={Smartphone} />,
      title: 'Mobile Friendly',
      description: 'Access Flyt on any device - desktop, tablet, or mobile.',
    },
    {
      icon: <LucideIcon icon={Globe} />,
      title: 'Global Reach',
      description: 'Connect with real estate professionals worldwide.',
    },
    {
      icon: <LucideIcon icon={FileText} />,
      title: 'Smart Documents',
      description: 'Manage resumes, portfolios, and feedback in one place.',
    },
    {
      icon: <LucideIcon icon={Play} />,
      title: 'Video Profiles',
      description: 'Create video introductions to stand out from the crowd.',
    },
  ];

  return (
    <div className="home-page">
      {/* Hero Section */}
      <div className="hero-section py-16 md:py-24 bg-gradient-to-r from-primary/10 via-primary/5 to-secondary/20 rounded-b-3xl overflow-hidden relative">
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-primary/5 rounded-full -mr-48 -mt-48"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-primary/5 rounded-full -ml-32 -mb-32"></div>

        <Row
          gutter={[32, 48]}
          align="middle"
          className="max-w-7xl mx-auto px-4 relative z-10"
        >
          <Col
            xs={24}
            md={12}
            className="animate-fadeIn"
          >
            <div className="hero-content">
              <div className="inline-block px-4 py-2 bg-primary/10 rounded-full mb-6 backdrop-blur-sm">
                <Text
                  strong
                  className="text-primary"
                >
                  <LucideIcon
                    icon={HomeIcon}
                    className="mr-2"
                  />{' '}
                  Real Estate Hiring Platform
                </Text>
              </div>
              <Title
                level={1}
                className="text-4xl md:text-5xl font-bold mb-6"
              >
                The Smarter Way to Hire in <span className="text-primary">Real Estate</span>
              </Title>
              <Paragraph className="text-lg md:text-xl text-muted mb-8">
                Flyt connects real estate companies with top talent through AI-powered matching and
                expert interviewers specialized in the property sector.
              </Paragraph>
              <Space
                size="large"
                className="mb-8"
              >
                <Button
                  type="primary"
                  size="large"
                  icon={<LucideIcon icon={Rocket} />}
                  className="rounded-full px-8 h-12 shadow-md hover:shadow-lg transition-all"
                >
                  Get Started
                </Button>
                <Button
                  size="large"
                  className="rounded-full px-8 h-12 border-2 flex items-center hover:border-primary hover:text-primary transition-all"
                >
                  Watch Demo{' '}
                  <LucideIcon
                    icon={ArrowRight}
                    className="ml-2"
                  />
                </Button>
              </Space>
              <div className="flex flex-col sm:flex-row items-start sm:items-center mt-8 gap-4">
                <div className="flex -space-x-2 mr-4">
                  {[1, 2, 3, 4].map((i) => (
                    <Avatar
                      key={i}
                      src={`https://randomuser.me/api/portraits/${i % 2 ? 'women' : 'men'}/${20 + i}.jpg`}
                      className="border-2 border-white shadow-sm"
                      size={40}
                    />
                  ))}
                </div>
                <div>
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <LucideIcon
                        key={i}
                        icon={Star}
                        className="text-yellow-400 text-lg"
                      />
                    ))}
                  </div>
                  <Text className="text-muted">
                    Trusted by <span className="font-semibold text-primary">200+</span> real estate
                    companies
                  </Text>
                </div>
              </div>
            </div>
          </Col>
          <Col
            xs={24}
            md={12}
            className="animate-fadeInRight"
          >
            <div className="hero-image-container relative">
              <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full -z-10"></div>
              <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-secondary/20 rounded-full -z-10"></div>
              <img
                src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
                alt="Real estate professionals"
                className="w-full h-auto rounded-2xl shadow-xl"
              />
              <div className="absolute -bottom-5 -right-5 bg-white dark:bg-card p-4 rounded-xl shadow-lg max-w-xs">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                    <LucideIcon icon={CheckCircle} />
                  </div>
                  <div>
                    <Text strong>Trusted Platform</Text>
                    <div className="text-xs text-muted">5,000+ successful placements</div>
                  </div>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      {/* Stats Section */}
      <div className="stats-section py-12 max-w-7xl mx-auto">
        <Row
          justify="center"
          gutter={[24, 24]}
          className="px-4"
        >
          <Col
            xs={24}
            sm={8}
            md={6}
          >
            <Card className="text-center border-0 shadow-md hover:shadow-lg transition-all rounded-xl overflow-hidden">
              <Statistic
                title={<span className="text-muted">Real Estate Jobs</span>}
                value={1500}
                suffix="+"
                valueStyle={{ color: 'var(--primary)' }}
              />
            </Card>
          </Col>
          <Col
            xs={24}
            sm={8}
            md={6}
          >
            <Card className="text-center border-0 shadow-md hover:shadow-lg transition-all rounded-xl overflow-hidden">
              <Statistic
                title={<span className="text-muted">Specialized Interviewers</span>}
                value={300}
                suffix="+"
                valueStyle={{ color: 'var(--primary)' }}
              />
            </Card>
          </Col>
          <Col
            xs={24}
            sm={8}
            md={6}
          >
            <Card className="text-center border-0 shadow-md hover:shadow-lg transition-all rounded-xl overflow-hidden">
              <Statistic
                title={<span className="text-muted">Successful Placements</span>}
                value={5000}
                suffix="+"
                valueStyle={{ color: 'var(--primary)' }}
              />
            </Card>
          </Col>
          <Col
            xs={24}
            sm={8}
            md={6}
          >
            <Card className="text-center border-0 shadow-md hover:shadow-lg transition-all rounded-xl overflow-hidden">
              <Statistic
                title={<span className="text-muted">Time-to-Hire Reduction</span>}
                value={50}
                suffix="%"
                valueStyle={{ color: 'var(--primary)' }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      {/* User Types Section */}
      <div className="user-types-section py-20 bg-gradient-to-b from-white to-secondary/10 dark:from-background dark:to-secondary/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 bg-primary/10 rounded-full mb-4">
              <Text
                strong
                className="text-primary"
              >
                Who We Serve
              </Text>
            </div>
            <Title
              level={2}
              className="mb-4 text-3xl md:text-4xl"
            >
              A Platform for Everyone in Real Estate
            </Title>
            <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
              Whether you're looking for a job, hiring talent, or want to leverage your expertise as
              an interviewer, Flyt has you covered.
            </Paragraph>
          </div>

          <Row gutter={[32, 32]}>
            {userTypes.map((type, index) => (
              <Col
                xs={24}
                md={8}
                key={index}
              >
                <Card
                  className={`h-full hover:shadow-xl transition-all hover:-translate-y-2 border-0 rounded-2xl overflow-hidden ${type.color}`}
                  bordered={false}
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-white/20 mb-6 text-white">
                    <span className="text-2xl">{type.icon}</span>
                  </div>
                  <Title
                    level={3}
                    className={`mb-3 ${type.textColor}`}
                  >
                    {type.title}
                  </Title>
                  <Paragraph className={`${type.textColor} opacity-90 mb-6`}>
                    {type.description}
                  </Paragraph>

                  <div className="mb-6">
                    {type.benefits.map((benefit, i) => (
                      <div
                        key={i}
                        className="flex items-center mb-2"
                      >
                        <LucideIcon
                          icon={CheckCircle}
                          className="mr-2 text-white"
                        />
                        <Text className={`${type.textColor}`}>{benefit}</Text>
                      </div>
                    ))}
                  </div>

                  <Button
                    type="default"
                    className="rounded-full bg-white hover:bg-white/90 text-primary border-0 shadow-md hover:shadow-lg"
                  >
                    {type.buttonText}
                  </Button>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* App Features Section */}
      <div className="features-section py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 bg-primary/10 rounded-full mb-4">
              <Text
                strong
                className="text-primary"
              >
                Why Choose Flyt
              </Text>
            </div>
            <Title
              level={2}
              className="mb-4 text-3xl md:text-4xl"
            >
              Streamlined Real Estate Hiring
            </Title>
            <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
              Our platform offers everything you need to find or fill real estate positions
              efficiently.
            </Paragraph>
          </div>

          <Row gutter={[32, 32]}>
            {features.map((feature, index) => (
              <Col
                xs={24}
                sm={12}
                lg={6}
                key={index}
              >
                <Card
                  className="feature-card h-full text-center hover:shadow-xl transition-all hover:-translate-y-2 border-0 rounded-2xl overflow-hidden"
                  bordered={false}
                >
                  <div
                    className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-6 text-white bg-gradient-to-r ${feature.color}`}
                  >
                    <span className="text-2xl">{feature.icon}</span>
                  </div>
                  <Title
                    level={4}
                    className="mb-3"
                  >
                    {feature.title}
                  </Title>
                  <Paragraph className="text-muted">{feature.description}</Paragraph>
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/* Real Estate Roles */}
      <div className="roles-section py-16 max-w-7xl mx-auto px-4 bg-gradient-to-b from-white to-secondary/10 dark:from-background dark:to-secondary/5 rounded-3xl">
        <div className="text-center mb-12">
          <div className="inline-block px-4 py-2 bg-primary/10 rounded-full mb-4">
            <Text
              strong
              className="text-primary"
            >
              Specialized Roles
            </Text>
          </div>
          <Title
            level={2}
            className="mb-4 text-3xl md:text-4xl"
          >
            Real Estate Positions We Cover
          </Title>
          <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
            Our platform specializes in connecting talent with companies across all real estate
            roles.
          </Paragraph>
        </div>

        <Row
          gutter={[16, 16]}
          className="mb-8"
        >
          {realEstateRoles.map((role, index) => (
            <Col
              xs={24}
              sm={12}
              md={6}
              key={index}
            >
              <Card
                className="text-center hover:border-primary hover:shadow-lg transition-all cursor-pointer rounded-xl overflow-hidden"
                bordered
              >
                <div className="flex items-center justify-center h-24">
                  <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                    {role.icon}
                  </div>
                  <div className="ml-4">
                    <Title
                      level={4}
                      className="!m-0!"
                    >
                      {role.title}
                    </Title>
                    <Text className="!m-0! text-muted!">{role.count}+ Positions</Text>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* Testimonials Section */}
      <div className="testimonials-section py-16">
        <div className="text-center mb-12">
          <Title
            level={2}
            className="mb-4"
          >
            What Our Users Say
          </Title>
          <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
            Hear from the companies and candidates who have transformed their interview process.
          </Paragraph>
        </div>

        <Row gutter={[24, 24]}>
          {testimonials.map((testimonial, index) => (
            <Col
              xs={24}
              md={8}
              key={index}
            >
              <Card
                className="testimonial-card h-full hover:shadow-md transition-shadow"
                bordered={false}
              >
                <div className="flex items-center mb-4">
                  <LucideIcon
                    icon={Star}
                    className="text-warning mr-1"
                  />
                  <LucideIcon
                    icon={Star}
                    className="text-warning mr-1"
                  />
                  <LucideIcon
                    icon={Star}
                    className="text-warning mr-1"
                  />
                  <LucideIcon
                    icon={Star}
                    className="text-warning mr-1"
                  />
                  <LucideIcon
                    icon={Star}
                    className="text-warning"
                  />
                </div>
                <Paragraph className="text-lg mb-6">"{testimonial.content}"</Paragraph>
                <div className="flex items-center">
                  <Avatar
                    src={testimonial.avatar}
                    size={48}
                    className="mr-4"
                  />
                  <div>
                    <Text
                      strong
                      className="block"
                    >
                      {testimonial.name}
                    </Text>
                    <Text className="text-muted">{testimonial.role}</Text>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>

      {/* For Candidates & Companies Section */}
      <div className="for-section py-16 bg-secondary">
        <div className="text-center mb-12">
          <Title
            level={2}
            className="mb-4"
          >
            Perfect for Everyone
          </Title>
          <Paragraph className="text-lg max-w-2xl mx-auto text-muted">
            Whether you're hiring or job hunting, our platform streamlines the interview process.
          </Paragraph>
        </div>

        <Row gutter={[32, 32]}>
          <Col
            xs={24}
            md={12}
          >
            <Card
              className="h-full hover:shadow-md transition-shadow"
              bordered={false}
            >
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary mb-4">
                  <LucideIcon
                    icon={User}
                    className="text-2xl text-white"
                  />
                </div>
                <Title level={3}>For Candidates</Title>
              </div>
              <ul className="space-y-3 mb-8 ">
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Showcase your skills to top companies</span>
                </li>
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Schedule interviews at your convenience</span>
                </li>
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Prepare with industry-specific resources</span>
                </li>
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Get feedback to improve your interview skills</span>
                </li>
              </ul>
              <div className="text-center">
                <Button
                  type="primary"
                  size="large"
                >
                  Sign Up as Candidate
                </Button>
              </div>
            </Card>
          </Col>

          <Col
            xs={24}
            md={12}
          >
            <Card
              className="h-full hover:shadow-md transition-shadow"
              bordered={false}
            >
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary mb-4">
                  <LucideIcon
                    icon={Users}
                    className="text-2xl text-white"
                  />
                </div>
                <Title level={3}>For Companies</Title>
              </div>
              <ul className="space-y-3 mb-8">
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Find qualified candidates faster</span>
                </li>
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Streamline your interview process</span>
                </li>
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Collaborate with your hiring team</span>
                </li>
                <li className="flex items-start">
                  <LucideIcon
                    icon={CheckCircle}
                    className="mt-1 mr-3 text-primary"
                  />
                  <span>Make data-driven hiring decisions</span>
                </li>
              </ul>
              <div className="text-center">
                <Button
                  type="primary"
                  size="large"
                >
                  Sign Up as Company
                </Button>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* CTA Section */}
      <div className="cta-section py-16">
        <Card
          className="text-center"
          bordered={false}
          style={{ background: 'var(--primary)', color: 'white' }}
        >
          <Title
            level={2}
            style={{ color: 'white' }}
            className="mb-4"
          >
            Ready to Transform Your Interview Process?
          </Title>
          <Paragraph
            style={{ color: 'rgba(255, 255, 255, 0.8)' }}
            className="text-lg mb-8 max-w-2xl mx-auto"
          >
            Join thousands of companies and candidates who have already improved their interview
            experience.
          </Paragraph>
          <Space size="large">
            <Button
              size="large"
              style={{
                background: 'white',
                color: 'var(--primary)',
                borderColor: 'white',
                fontWeight: 500,
              }}
            >
              Get Started Free
            </Button>
            <Button
              ghost
              size="large"
              style={{
                color: 'white',
                borderColor: 'white',
                fontWeight: 500,
              }}
            >
              Schedule Demo
            </Button>
          </Space>
        </Card>
      </div>
    </div>
  );
};

export default Home;
